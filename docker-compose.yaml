services:

  postgres_db:
    image: postgres:15
    container_name: postgres_db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: product_crud
    ports:
      - 5432:5432
    volumes:
      - "../_fixtures/.pgdata:/var/lib/postgresql/data"
    restart: always


  pgadmin:
    image: dpage/pgadmin4
    container_name: pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - 8083:80
    restart: always

  redis:
    image: redis:7-alpine
    container_name: redis_cache
    ports:
      - 6379:6379
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: always


  ###> doctrine/doctrine-bundle ###
  database:
    image: postgres:${POSTGRES_VERSION:-16}-alpine
    environment:
      POSTGRES_DB: postgresdb
      # You should definitely change the password in production
      POSTGRES_PASSWORD: postgres
      POSTGRES_USER: postgres
    volumes:
      - database_data:/var/lib/postgresql/data:rw
      # You may use a bind-mounted host directory instead, so that it is harder to accidentally remove the volume and lose all your data!
      # - ./docker/db/data:/var/lib/postgresql/data:rw
###< doctrine/doctrine-bundle ###

volumes:
  ###> doctrine/doctrine-bundle ###
  database_data:
  redis_data:
###< doctrine/doctrine-bundle ###


