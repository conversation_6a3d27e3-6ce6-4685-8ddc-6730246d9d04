<?php

namespace App\Controller;

use App\Entity\Author;
use App\Entity\Product;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/product', name: 'product')]
final class ProductController extends AbstractController
{
#[Route('/create', name: 'create')]
    
    public function index(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $product = new Product();
        $product->setName($data['name']);
        $product->setPrice($data['price']);
        $em->persist($product);
        $em->flush();
        return new JsonResponse(['message' => 'Product created']);
        
    }

    #[Route('/author', name: 'create_author')]
    public function createAuthor(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $product = new Author();
        $product->setName($data['name']);
        $product->setAge($data['age']);
        $product->setGender($data['gender']);
        $em->persist($product);
        $em->flush();
        return new JsonResponse(['message' => 'Author created']);
        
    }
}